# OpenRouter Setup Instructions

## 1. Get Your OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for an account or log in
3. Go to your [API Keys page](https://openrouter.ai/keys)
4. Create a new API key
5. Copy the API key

## 2. Configure Environment Variables

1. Open the `.env.local` file in your project root
2. Replace `your_openrouter_api_key_here` with your actual API key:

```env
OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## 3. Available Models

The current setup uses `anthropic/claude-3.5-sonnet`, but you can change it to other models:

### Popular Options:
- `anthropic/claude-3.5-sonnet` (Default - Great for content generation)
- `openai/gpt-4o` (OpenAI's latest model)
- `openai/gpt-3.5-turbo` (Faster and cheaper)
- `meta-llama/llama-3.1-8b-instruct:free` (Free option)
- `google/gemini-pro` (Google's model)

### To Change Model:
Edit `src/app/api/generate/route.ts` line 42:
```typescript
model: 'your-preferred-model-here',
```

## 4. Restart the Server

After adding your API key:
1. Stop the development server (Ctrl+C)
2. Restart it: `npm run dev`

## 5. Test the Integration

1. Fill in the form fields
2. Click "Generate Content"
3. You should see AI-generated content instead of mock data

## 6. Pricing

- Check [OpenRouter pricing](https://openrouter.ai/docs#models) for model costs
- Most models charge per token (input + output)
- Free tier available with some models

## 7. Troubleshooting

### Common Issues:
- **"OpenRouter API key not configured"**: Check your `.env.local` file
- **"Failed to generate content"**: Check your API key and internet connection
- **Server not restarting**: Make sure to restart after adding environment variables

### Debug Mode:
Check the browser console and terminal for error messages if generation fails.
