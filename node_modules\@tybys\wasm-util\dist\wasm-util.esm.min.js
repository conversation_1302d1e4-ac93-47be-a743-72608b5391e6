const t="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0;if(!t)throw new Error("WebAssembly is not supported in this environment");function e(t,e){if(null===t||"object"!=typeof t)throw new TypeError(`${e} must be an object. Received ${null===t?"null":typeof t}`)}function n(t,e){if("string"!=typeof t)throw new TypeError(`${e} must be a string. Received ${null===t?"null":typeof t}`)}function r(t,e){if("function"!=typeof t)throw new TypeError(`${e} must be a function. Received ${null===t?"null":typeof t}`)}function i(t,e){if(void 0!==t)throw new TypeError(`${e} must be undefined. Received ${null===t?"null":typeof t}`)}function s(t){return!(!t||"object"!=typeof t&&"function"!=typeof t||"function"!=typeof t.then)}function o(t,e){const n=Object.create(null);return Object.keys(t).forEach((r=>{const i=t[r];Object.defineProperty(n,r,{enumerable:!0,value:e(i,r)})})),n}function a(t,e,n){return"function"==typeof SharedArrayBuffer&&t.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(t.buffer.constructor)?t.slice(e,n):t.subarray(e,n)}const c=["asyncify_get_state","asyncify_start_rewind","asyncify_start_unwind","asyncify_stop_rewind","asyncify_stop_unwind"];function u(t,e,n,r){if("function"!=typeof t.exports[r]||n<=0)return{wasm64:e,dataPtr:16,start:e?32:24,end:1024};const i=t.exports[r],s=e?Number(i(BigInt(16)+BigInt(n))):i(8+n);if(0===s)throw new Error("Allocate asyncify data failed");return e?{wasm64:e,dataPtr:s,start:s+16,end:s+16+n}:{wasm64:e,dataPtr:s,start:s+8,end:s+8+n}}class f{constructor(){this.value=void 0,this.exports=void 0,this.dataPtr=0}init(e,n,r){var i,s;if(this.exports)throw new Error("Asyncify has been initialized");if(!(e instanceof t.Memory))throw new TypeError("Require WebAssembly.Memory object");const o=n.exports;for(let t=0;t<c.length;++t)if("function"!=typeof o[c[t]])throw new TypeError("Invalid asyncify wasm");let a;const f=Boolean(r.wasm64);a=r.tryAllocate?!0===r.tryAllocate?u(n,f,4096,"malloc"):u(n,f,null!==(i=r.tryAllocate.size)&&void 0!==i?i:4096,null!==(s=r.tryAllocate.name)&&void 0!==s?s:"malloc"):{wasm64:f,dataPtr:16,start:f?32:24,end:1024},this.dataPtr=a.dataPtr,f?new BigInt64Array(e.buffer,this.dataPtr).set([BigInt(a.start),BigInt(a.end)]):new Int32Array(e.buffer,this.dataPtr).set([a.start,a.end]),this.exports=this.wrapExports(o,r.wrapExports);const h=Object.create(t.Instance.prototype);return Object.defineProperty(h,"exports",{value:this.exports}),h}assertState(){if(0!==this.exports.asyncify_get_state())throw new Error("Asyncify state error")}wrapImportFunction(t){const e=this;return function(){for(;2===e.exports.asyncify_get_state();)return e.exports.asyncify_stop_rewind(),e.value;e.assertState();const n=t.apply(this,arguments);if(!s(n))return n;e.exports.asyncify_start_unwind(e.dataPtr),e.value=n}}wrapImports(t){const e={};return Object.keys(t).forEach((n=>{const r=t[n],i={};Object.keys(r).forEach((t=>{const e=r[t];i[t]="function"==typeof e?this.wrapImportFunction(e):e})),e[n]=i})),e}wrapExportFunction(t){const e=this;return async function(){e.assertState();let n=t.apply(this,arguments);for(;1===e.exports.asyncify_get_state();)e.exports.asyncify_stop_unwind(),e.value=await e.value,e.assertState(),e.exports.asyncify_start_rewind(e.dataPtr),n=t.call(this);return e.assertState(),n}}wrapExports(t,e){return o(t,((t,n)=>{let r=-1!==c.indexOf(n)||"function"!=typeof t;return Array.isArray(e)&&(r=r||-1===e.indexOf(n)),r?t:this.wrapExportFunction(t)}))}}function h(t){if(t&&"object"!=typeof t)throw new TypeError("imports must be an object or undefined")}function g(e,n){return"undefined"!=typeof wx&&"undefined"!=typeof __wxConfig?t.instantiate(e,n):fetch(e).then((t=>t.arrayBuffer())).then((e=>t.instantiate(e,n)))}function d(e,n){let r;if(h(n),n=null!=n?n:{},e instanceof ArrayBuffer||ArrayBuffer.isView(e))return t.instantiate(e,n);if(e instanceof t.Module)return t.instantiate(e,n).then((t=>({instance:t,module:e})));if("string"!=typeof e&&!(e instanceof URL))throw new TypeError("Invalid source");if("function"==typeof t.instantiateStreaming){let i;try{i=fetch(e),r=t.instantiateStreaming(i,n).catch((()=>g(e,n)))}catch(t){r=g(e,n)}}else r=g(e,n);return r}function l(t,e,n){h(n),n=null!=n?n:{};const r=new f;return d(e,n=r.wrapImports(n)).then((e=>{var i;const s=e.instance.exports.memory||(null===(i=n.env)||void 0===i?void 0:i.memory);return{module:e.module,instance:r.init(s,e.instance,t)}}))}function _(e,n){let r;if(h(n),n=null!=n?n:{},e instanceof ArrayBuffer||ArrayBuffer.isView(e))r=new t.Module(e);else{if(!(e instanceof WebAssembly.Module))throw new TypeError("Invalid source");r=e}return{instance:new t.Instance(r,n),module:r}}function E(t,e,n){var r;h(n),n=null!=n?n:{};const i=new f,s=_(e,n=i.wrapImports(n)),o=s.instance.exports.memory||(null===(r=n.env)||void 0===r?void 0:r.memory);return{module:s.module,instance:i.init(o,s.instance,t)}}const p=46,y=47;function I(t){return t===y}function m(...t){let e="",r=!1;for(let i=t.length-1;i>=-1&&!r;i--){const s=i>=0?t[i]:"/";n(s,"path"),0!==s.length&&(e=`${s}/${e}`,r=s.charCodeAt(0)===y)}return e=function(t,e,n,r){let i="",s=0,o=-1,a=0,c=0;for(let u=0;u<=t.length;++u){if(u<t.length)c=t.charCodeAt(u);else{if(r(c))break;c=y}if(r(c)){if(o===u-1||1===a);else if(2===a){if(i.length<2||2!==s||i.charCodeAt(i.length-1)!==p||i.charCodeAt(i.length-2)!==p){if(i.length>2){const t=i.indexOf(n);-1===t?(i="",s=0):(i=i.slice(0,t),s=i.length-1-i.indexOf(n)),o=u,a=0;continue}if(0!==i.length){i="",s=0,o=u,a=0;continue}}e&&(i+=i.length>0?`${n}..`:"..",s=2)}else i.length>0?i+=`${n}${t.slice(o+1,u)}`:i=t.slice(o+1,u),s=u-o-1;o=u,a=0}else c===p&&-1!==a?++a:a=-1}return i}(e,!r,"/",I),r?`/${e}`:e.length>0?e:"."}const A={FD_DATASYNC:BigInt(1)<<BigInt(0),FD_READ:BigInt(1)<<BigInt(1),FD_SEEK:BigInt(1)<<BigInt(2),FD_FDSTAT_SET_FLAGS:BigInt(1)<<BigInt(3),FD_SYNC:BigInt(1)<<BigInt(4),FD_TELL:BigInt(1)<<BigInt(5),FD_WRITE:BigInt(1)<<BigInt(6),FD_ADVISE:BigInt(1)<<BigInt(7),FD_ALLOCATE:BigInt(1)<<BigInt(8),PATH_CREATE_DIRECTORY:BigInt(1)<<BigInt(9),PATH_CREATE_FILE:BigInt(1)<<BigInt(10),PATH_LINK_SOURCE:BigInt(1)<<BigInt(11),PATH_LINK_TARGET:BigInt(1)<<BigInt(12),PATH_OPEN:BigInt(1)<<BigInt(13),FD_READDIR:BigInt(1)<<BigInt(14),PATH_READLINK:BigInt(1)<<BigInt(15),PATH_RENAME_SOURCE:BigInt(1)<<BigInt(16),PATH_RENAME_TARGET:BigInt(1)<<BigInt(17),PATH_FILESTAT_GET:BigInt(1)<<BigInt(18),PATH_FILESTAT_SET_SIZE:BigInt(1)<<BigInt(19),PATH_FILESTAT_SET_TIMES:BigInt(1)<<BigInt(20),FD_FILESTAT_GET:BigInt(1)<<BigInt(21),FD_FILESTAT_SET_SIZE:BigInt(1)<<BigInt(22),FD_FILESTAT_SET_TIMES:BigInt(1)<<BigInt(23),PATH_SYMLINK:BigInt(1)<<BigInt(24),PATH_REMOVE_DIRECTORY:BigInt(1)<<BigInt(25),PATH_UNLINK_FILE:BigInt(1)<<BigInt(26),POLL_FD_READWRITE:BigInt(1)<<BigInt(27),SOCK_SHUTDOWN:BigInt(1)<<BigInt(28),SOCK_ACCEPT:BigInt(1)<<BigInt(29)};class b extends Error{constructor(t,e){super(t),this.errno=e}getErrorMessage(){return function(t){switch(t){case 0:return"Success";case 1:return"Argument list too long";case 2:return"Permission denied";case 3:return"Address in use";case 4:return"Address not available";case 5:return"Address family not supported by protocol";case 6:return"Resource temporarily unavailable";case 7:return"Operation already in progress";case 8:return"Bad file descriptor";case 9:return"Bad message";case 10:return"Resource busy";case 11:return"Operation canceled";case 12:return"No child process";case 13:return"Connection aborted";case 14:return"Connection refused";case 15:return"Connection reset by peer";case 16:return"Resource deadlock would occur";case 17:return"Destination address required";case 18:return"Domain error";case 19:return"Quota exceeded";case 20:return"File exists";case 21:return"Bad address";case 22:return"File too large";case 23:return"Host is unreachable";case 24:return"Identifier removed";case 25:return"Illegal byte sequence";case 26:return"Operation in progress";case 27:return"Interrupted system call";case 28:return"Invalid argument";case 29:return"I/O error";case 30:return"Socket is connected";case 31:return"Is a directory";case 32:return"Symbolic link loop";case 33:return"No file descriptors available";case 34:return"Too many links";case 35:return"Message too large";case 36:return"Multihop attempted";case 37:return"Filename too long";case 38:return"Network is down";case 39:return"Connection reset by network";case 40:return"Network unreachable";case 41:return"Too many files open in system";case 42:return"No buffer space available";case 43:return"No such device";case 44:return"No such file or directory";case 45:return"Exec format error";case 46:return"No locks available";case 47:return"Link has been severed";case 48:return"Out of memory";case 49:return"No message of the desired type";case 50:return"Protocol not available";case 51:return"No space left on device";case 52:return"Function not implemented";case 53:return"Socket not connected";case 54:return"Not a directory";case 55:return"Directory not empty";case 56:return"State not recoverable";case 57:return"Not a socket";case 58:return"Not supported";case 59:return"Not a tty";case 60:return"No such device or address";case 61:return"Value too large for data type";case 62:return"Previous owner died";case 63:return"Operation not permitted";case 64:return"Broken pipe";case 65:return"Protocol error";case 66:return"Protocol not supported";case 67:return"Protocol wrong type for socket";case 68:return"Result not representable";case 69:return"Read-only file system";case 70:return"Invalid seek";case 71:return"No such process";case 72:return"Stale file handle";case 73:return"Operation timed out";case 74:return"Text file busy";case 75:return"Cross-device link";case 76:return"Capabilities insufficient";default:return"Unknown error"}}(this.errno)}}Object.defineProperty(b.prototype,"name",{configurable:!0,writable:!0,value:"WasiError"});const T=A.FD_DATASYNC|A.FD_READ|A.FD_SEEK|A.FD_FDSTAT_SET_FLAGS|A.FD_SYNC|A.FD_TELL|A.FD_WRITE|A.FD_ADVISE|A.FD_ALLOCATE|A.PATH_CREATE_DIRECTORY|A.PATH_CREATE_FILE|A.PATH_LINK_SOURCE|A.PATH_LINK_TARGET|A.PATH_OPEN|A.FD_READDIR|A.PATH_READLINK|A.PATH_RENAME_SOURCE|A.PATH_RENAME_TARGET|A.PATH_FILESTAT_GET|A.PATH_FILESTAT_SET_SIZE|A.PATH_FILESTAT_SET_TIMES|A.FD_FILESTAT_GET|A.FD_FILESTAT_SET_TIMES|A.FD_FILESTAT_SET_SIZE|A.PATH_SYMLINK|A.PATH_UNLINK_FILE|A.PATH_REMOVE_DIRECTORY|A.POLL_FD_READWRITE|A.SOCK_SHUTDOWN|A.SOCK_ACCEPT,w=T,B=T,N=T,S=T,P=A.FD_DATASYNC|A.FD_READ|A.FD_SEEK|A.FD_FDSTAT_SET_FLAGS|A.FD_SYNC|A.FD_TELL|A.FD_WRITE|A.FD_ADVISE|A.FD_ALLOCATE|A.FD_FILESTAT_GET|A.FD_FILESTAT_SET_SIZE|A.FD_FILESTAT_SET_TIMES|A.POLL_FD_READWRITE,D=BigInt(0),F=A.FD_FDSTAT_SET_FLAGS|A.FD_SYNC|A.FD_ADVISE|A.PATH_CREATE_DIRECTORY|A.PATH_CREATE_FILE|A.PATH_LINK_SOURCE|A.PATH_LINK_TARGET|A.PATH_OPEN|A.FD_READDIR|A.PATH_READLINK|A.PATH_RENAME_SOURCE|A.PATH_RENAME_TARGET|A.PATH_FILESTAT_GET|A.PATH_FILESTAT_SET_SIZE|A.PATH_FILESTAT_SET_TIMES|A.FD_FILESTAT_GET|A.FD_FILESTAT_SET_TIMES|A.PATH_SYMLINK|A.PATH_UNLINK_FILE|A.PATH_REMOVE_DIRECTORY|A.POLL_FD_READWRITE,U=F|P,v=A.FD_READ|A.FD_FDSTAT_SET_FLAGS|A.FD_WRITE|A.FD_FILESTAT_GET|A.POLL_FD_READWRITE|A.SOCK_SHUTDOWN,R=T,L=A.FD_READ|A.FD_FDSTAT_SET_FLAGS|A.FD_WRITE|A.FD_FILESTAT_GET|A.POLL_FD_READWRITE,H=BigInt(0);function O(t,e,n,r){const i={base:BigInt(0),inheriting:BigInt(0)};if(0===r)throw new b("Unknown file type",28);switch(r){case 4:i.base=P,i.inheriting=D;break;case 3:i.base=F,i.inheriting=U;break;case 6:case 5:i.base=v,i.inheriting=R;break;case 2:-1!==t.indexOf(e)?(i.base=L,i.inheriting=H):(i.base=N,i.inheriting=S);break;case 1:i.base=w,i.inheriting=B;break;default:i.base=BigInt(0),i.inheriting=BigInt(0)}const s=3&n;return 0===s?i.base&=~A.FD_WRITE:1===s&&(i.base&=~A.FD_READ),i}function k(t,e){let n=0;if("number"==typeof e&&e>=0)n=e;else for(let e=0;e<t.length;e++){n+=t[e].length}let r=0;const i=new Uint8Array(n);for(let e=0;e<t.length;e++){const n=t[e];i.set(n,r),r+=n.length}return i}class C{constructor(t,e,n,r,i,s,o,a){this.id=t,this.fd=e,this.path=n,this.realPath=r,this.type=i,this.rightsBase=s,this.rightsInheriting=o,this.preopen=a,this.pos=BigInt(0),this.size=BigInt(0)}seek(t,e){if(0===e)this.pos=BigInt(t);else if(1===e)this.pos+=BigInt(t);else{if(2!==e)throw new b("Unknown whence",29);this.pos=BigInt(this.size)-BigInt(t)}return this.pos}}class x extends C{constructor(t,e,n,r,i,s,o,a,c){super(e,n,r,i,s,o,a,c),this._log=t,this._buf=null}write(t){const e=t;if(this._buf&&(t=k([this._buf,t]),this._buf=null),-1===t.indexOf(10))return this._buf=t,e.byteLength;let n,r=0,i=0;for(;-1!==(n=t.indexOf(10,r));){const e=(new TextDecoder).decode(t.subarray(i,n));this._log(e),r+=n-i+1,i=n+1}return r<t.length&&(this._buf=t.slice(r)),e.byteLength}}function M(t){return t.isBlockDevice()?1:t.isCharacterDevice()?2:t.isDirectory()?3:t.isSocket()?6:t.isFile()?4:t.isSymbolicLink()?7:0}function K(t,e,n){t.setBigUint64(e,n.dev,!0),t.setBigUint64(e+8,n.ino,!0),t.setBigUint64(e+16,BigInt(M(n)),!0),t.setBigUint64(e+24,n.nlink,!0),t.setBigUint64(e+32,n.size,!0),t.setBigUint64(e+40,n.atimeMs*BigInt(1e6),!0),t.setBigUint64(e+48,n.mtimeMs*BigInt(1e6),!0),t.setBigUint64(e+56,n.ctimeMs*BigInt(1e6),!0)}class W{constructor(t){this.used=0,this.size=t.size,this.fds=Array(t.size),this.stdio=[t.in,t.out,t.err],this.print=t.print,this.printErr=t.printErr,this.insertStdio(t.in,0,"<stdin>"),this.insertStdio(t.out,1,"<stdout>"),this.insertStdio(t.err,2,"<stderr>")}insertStdio(t,e,n){const{base:r,inheriting:i}=O(this.stdio,t,2,2),s=this.insert(t,n,n,2,r,i,0);if(s.id!==e)throw new b(`id: ${s.id} !== expected: ${e}`,8);return s}insert(t,e,n,r,i,s,o){var a,c;let u,f=-1;if(this.used>=this.size){const t=2*this.size;this.fds.length=t,f=this.size,this.size=t}else for(let t=0;t<this.size;++t)if(null==this.fds[t]){f=t;break}return u="<stdout>"===e?new x(null!==(a=this.print)&&void 0!==a?a:console.log,f,t,e,n,r,i,s,o):"<stderr>"===e?new x(null!==(c=this.printErr)&&void 0!==c?c:console.error,f,t,e,n,r,i,s,o):new C(f,t,e,n,r,i,s,o),this.fds[f]=u,this.used++,u}get(t,e,n){if(t>=this.size)throw new b("Invalid fd",8);const r=this.fds[t];if(!r||r.id!==t)throw new b("Bad file descriptor",8);if((~r.rightsBase&e)!==BigInt(0)||(~r.rightsInheriting&n)!==BigInt(0))throw new b("Capabilities insufficient",76);return r}remove(t){if(t>=this.size)throw new b("Invalid fd",8);const e=this.fds[t];if(!e||e.id!==t)throw new b("Bad file descriptor",8);this.fds[t]=void 0,this.used--}}class z extends W{constructor(t){super(t),this.fs=t.fs}getFileTypeByFd(t){return M(this.fs.fstatSync(t,{bigint:!0}))}insertPreopen(t,e,n){const r=this.getFileTypeByFd(t);if(3!==r)throw new b(`Preopen not dir: ["${e}", "${n}"]`,54);const i=O(this.stdio,t,0,r);return this.insert(t,e,n,r,i.base,i.inheriting,1)}renumber(t,e){if(t===e)return;if(t>=this.size||e>=this.size)throw new b("Invalid fd",8);const n=this.fds[t],r=this.fds[e];if(!n||!r||n.id!==t||r.id!==e)throw new b("Invalid fd",8);this.fs.closeSync(n.fd),this.fds[t]=this.fds[e],this.fds[t].id=t,this.fds[e]=void 0,this.used--}}class G extends W{constructor(t){super(t)}async getFileTypeByFd(t){return M(await t.stat({bigint:!0}))}async insertPreopen(t,e,n){const r=await this.getFileTypeByFd(t);if(3!==r)throw new b(`Preopen not dir: ["${e}", "${n}"]`,54);const i=O(this.stdio,t.fd,0,r);return this.insert(t,e,n,r,i.base,i.inheriting,1)}async renumber(t,e){if(t===e)return;if(t>=this.size||e>=this.size)throw new b("Invalid fd",8);const n=this.fds[t],r=this.fds[e];if(!n||!r||n.id!==t||r.id!==e)throw new b("Invalid fd",8);await n.fd.close(),this.fds[t]=this.fds[e],this.fds[t].id=t,this.fds[e]=void 0,this.used--}}const j=function(){return t.Memory}();class Y extends j{constructor(t){super(t)}get HEAP8(){return new Int8Array(super.buffer)}get HEAPU8(){return new Uint8Array(super.buffer)}get HEAP16(){return new Int16Array(super.buffer)}get HEAPU16(){return new Uint16Array(super.buffer)}get HEAP32(){return new Int32Array(super.buffer)}get HEAPU32(){return new Uint32Array(super.buffer)}get HEAP64(){return new BigInt64Array(super.buffer)}get HEAPU64(){return new BigUint64Array(super.buffer)}get HEAPF32(){return new Float32Array(super.buffer)}get HEAPF64(){return new Float64Array(super.buffer)}get view(){return new DataView(super.buffer)}}function $(e){return Object.getPrototypeOf(e)===t.Memory.prototype&&Object.setPrototypeOf(e,Y.prototype),e}function V(){const e=t.Function;if("function"!=typeof e)throw new Error('WebAssembly.Function is not supported in this environment. If you are using V8 based browser like Chrome, try to specify --js-flags="--wasm-staging --experimental-wasm-stack-switching"');return e}function Z(t,e,n){const r=V();if("function"!=typeof t)throw new TypeError("Function required");const i=e.slice(0);return i.unshift("externref"),new r({parameters:i,results:n},t,{suspending:"first"})}function q(t){const e=V();if("function"!=typeof t)throw new TypeError("Function required");return new e({parameters:[...e.type(t).parameters.slice(1)],results:["externref"]},t,{promising:"first"})}function X(t,e){return o(t,((t,n)=>{let r="function"!=typeof t;return Array.isArray(e)&&(r=r||-1===e.indexOf(n)),r?t:q(t)}))}function Q(t,e){if(0===t.length||0===e.length)return 0;let n=0,r=e.length-n;for(let i=0;i<t.length;++i){const s=t[i];if(r<s.length)return s.set(e.subarray(n,n+r),0),n+=r,r=0,n;s.set(e.subarray(n,n+s.length),0),n+=s.length,r-=s.length}return n}const J=new WeakMap,tt=new WeakMap,et=new WeakMap;function nt(t){return J.get(t)}function rt(t){const e=et.get(t);if(!e)throw new Error("filesystem is unavailable");return e}function it(t){if(t instanceof b)return t.errno;switch(t.code){case"ENOENT":return 44;case"EBADF":return 8;case"EINVAL":return 28;case"EPERM":return 63;case"EPROTO":return 65;case"EEXIST":return 20;case"ENOTDIR":return 54;case"EMFILE":return 33;case"EACCES":return 2;case"EISDIR":return 31;case"ENOTEMPTY":return 55;case"ENOSYS":return 52}throw t}function st(t,e,n){return function(t,e){return Object.defineProperty(e,"name",{value:t}),e}(e,(function(){let e;try{e=n.apply(t,arguments)}catch(t){return it(t)}return s(e)?e.then((t=>t),it):e}))}function ot(t,e,n,r){let i=m(e.realPath,n);if(1==(1&r))try{i=t.readlinkSync(i)}catch(t){if("EINVAL"!==t.code&&"ENOENT"!==t.code)throw t}return i}async function at(t,e,n,r){let i=m(e.realPath,n);if(1==(1&r))try{i=await t.promises.readlink(i)}catch(t){if("EINVAL"!==t.code&&"ENOENT"!==t.code)throw t}return i}const ct=new TextEncoder,ut=new TextDecoder,ft=(BigInt(1)<<BigInt(63))-BigInt(1);function ht(){const t=window.prompt();if(null===t)return new Uint8Array;return(new TextEncoder).encode(t+"\n")}function gt(t){return Boolean(-16&t)||3==(3&t)||12==(12&t)}class dt{constructor(e,n,r,i,s,o){this.args_get=st(this,"args_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{HEAPU8:n,view:r}=nt(this),i=tt.get(this).args;for(let s=0;s<i.length;++s){const o=i[s];r.setInt32(t,e,!0),t+=4;const a=ct.encode(o+"\0");n.set(a,e),e+=a.length}return 0})),this.args_sizes_get=st(this,"args_sizes_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{view:n}=nt(this),r=tt.get(this).args;return n.setUint32(t,r.length,!0),n.setUint32(e,ct.encode(r.join("\0")+"\0").length,!0),0})),this.environ_get=st(this,"environ_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{HEAPU8:n,view:r}=nt(this),i=tt.get(this).env;for(let s=0;s<i.length;++s){const o=i[s];r.setInt32(t,e,!0),t+=4;const a=ct.encode(o+"\0");n.set(a,e),e+=a.length}return 0})),this.environ_sizes_get=st(this,"environ_sizes_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{view:n}=nt(this),r=tt.get(this);return n.setUint32(t,r.env.length,!0),n.setUint32(e,ct.encode(r.env.join("\0")+"\0").length,!0),0})),this.clock_res_get=st(this,"clock_res_get",(function(t,e){if(0===(e=Number(e)))return 28;const{view:n}=nt(this);switch(t){case 0:return n.setBigUint64(e,BigInt(1e6),!0),0;case 1:case 2:case 3:return n.setBigUint64(e,BigInt(1e3),!0),0;default:return 28}})),this.clock_time_get=st(this,"clock_time_get",(function(t,e,n){if(0===(n=Number(n)))return 28;const{view:r}=nt(this);switch(t){case 0:return r.setBigUint64(n,BigInt(Date.now())*BigInt(1e6),!0),0;case 1:case 2:case 3:{const t=performance.now(),e=Math.trunc(t),i=Math.floor(1e3*(t-e)),s=BigInt(e)*BigInt(1e9)+BigInt(i)*BigInt(1e6);return r.setBigUint64(n,s,!0),0}default:return 28}})),this.fd_advise=st(this,"fd_advise",(function(t,e,n,r){return 52})),this.fd_fdstat_get=st(this,"fd_fdstat_get",(function(t,e){if(0===(e=Number(e)))return 28;const n=tt.get(this).fds.get(t,BigInt(0),BigInt(0)),{view:r}=nt(this);return r.setUint16(e,n.type,!0),r.setUint16(e+2,0,!0),r.setBigUint64(e+8,n.rightsBase,!0),r.setBigUint64(e+16,n.rightsInheriting,!0),0})),this.fd_fdstat_set_flags=st(this,"fd_fdstat_set_flags",(function(t,e){return 52})),this.fd_fdstat_set_rights=st(this,"fd_fdstat_set_rights",(function(t,e,n){const r=tt.get(this).fds.get(t,BigInt(0),BigInt(0));return(e|r.rightsBase)>r.rightsBase||(n|r.rightsInheriting)>r.rightsInheriting?76:(r.rightsBase=e,r.rightsInheriting=n,0)})),this.fd_prestat_get=st(this,"fd_prestat_get",(function(t,e){if(0===(e=Number(e)))return 28;const n=tt.get(this);let r;try{r=n.fds.get(t,BigInt(0),BigInt(0))}catch(t){if(t instanceof b)return t.errno;throw t}if(1!==r.preopen)return 28;const{view:i}=nt(this);return i.setUint32(e,0,!0),i.setUint32(e+4,ct.encode(r.path).length,!0),0})),this.fd_prestat_dir_name=st(this,"fd_prestat_dir_name",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const r=tt.get(this).fds.get(t,BigInt(0),BigInt(0));if(1!==r.preopen)return 8;const i=ct.encode(r.path);if(i.length>n)return 42;const{HEAPU8:s}=nt(this);return s.set(i,e),0})),this.fd_seek=st(this,"fd_seek",(function(t,e,n,r){if(0===(r=Number(r)))return 28;if(0===t||1===t||2===t)return 0;const i=tt.get(this).fds.get(t,A.FD_SEEK,BigInt(0)).seek(e,n),{view:s}=nt(this);return s.setBigUint64(r,i,!0),0})),this.fd_tell=st(this,"fd_tell",(function(t,e){const n=tt.get(this).fds.get(t,A.FD_TELL,BigInt(0)),r=BigInt(n.pos),{view:i}=nt(this);return i.setBigUint64(Number(e),r,!0),0})),this.poll_oneoff=st(this,"poll_oneoff",(function(t,e,n,r){if(t=Number(t),e=Number(e),r=Number(r),n=Number(n),n>>>=0,0===t||0===e||0===n||0===r)return 28;const{view:i}=nt(this);i.setUint32(r,0,!0);let s,o=0,a=BigInt(0),c=BigInt(0),u=0,f=BigInt(0);const h=Array(n);for(o=0;o<n;o++){s=t+48*o;const e=i.getBigUint64(s,!0),n=i.getUint8(s+8),r=i.getUint32(s+16,!0),a=i.getBigUint64(s+24,!0),c=i.getBigUint64(s+32,!0),u=i.getUint16(s+40,!0);h[o]={userdata:e,type:n,u:{clock:{clock_id:r,timeout:a,precision:c,flags:u},fd_readwrite:{fd:r}}}}const g=[];for(o=0;o<n;o++)switch(s=h[o],s.type){case 0:if(1===s.u.clock.flags){const t=BigInt(Date.now())*BigInt(1e6);c=s.u.clock.timeout-t}else c=s.u.clock.timeout;(0===u||c<f)&&(f=c,a=s.userdata,u=1);break;case 1:case 2:g.push(s);break;default:return 28}if(g.length>0){for(o=0;o<g.length;o++){const t=g[o],n=e+32*o;i.setBigUint64(n,t.userdata,!0),i.setUint32(n+8,52,!0),i.setUint32(n+12,t.type,!0),i.setBigUint64(n+16,BigInt(0),!0),i.setUint16(n+24,0,!0),i.setUint32(r,1,!0)}return i.setUint32(r,g.length,!0),0}if(u){!function(t,e){const n=Date.now()+t;let r=!1;for(;Date.now()<n;)if(e()){r=!0;break}}(Number(f/BigInt(1e6)),(()=>!1));const t=e;i.setBigUint64(t,a,!0),i.setUint32(t+8,0,!0),i.setUint32(t+12,0,!0),i.setUint32(r,1,!0)}return 0})),this.proc_exit=st(this,"proc_exit",(function(t){return"object"==typeof process&&null!==process&&"function"==typeof process.exit&&process.exit(t),0})),this.proc_raise=st(this,"proc_raise",(function(t){return 52})),this.sched_yield=st(this,"sched_yield",(function(){return 0})),this.random_get="undefined"!=typeof crypto&&"function"==typeof crypto.getRandomValues?st(this,"random_get",(function(t,e){if(0===(t=Number(t)))return 28;e=Number(e);const{HEAPU8:n,view:r}=nt(this);if("function"==typeof SharedArrayBuffer&&n.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(n.buffer)){for(let n=t;n<t+e;++n)r.setUint8(n,Math.floor(256*Math.random()));return 0}let i;const s=65536;for(i=0;i+s<e;i+=s)crypto.getRandomValues(n.subarray(t+i,t+i+s));return crypto.getRandomValues(n.subarray(t+i,t+e)),0})):st(this,"random_get",(function(t,e){if(0===(t=Number(t)))return 28;e=Number(e);const{view:n}=nt(this);for(let r=t;r<t+e;++r)n.setUint8(r,Math.floor(256*Math.random()));return 0})),this.sock_recv=st(this,"sock_recv",(function(){return 58})),this.sock_send=st(this,"sock_send",(function(){return 58})),this.sock_shutdown=st(this,"sock_shutdown",(function(){return 58})),this.sock_accept=st(this,"sock_accept",(function(){return 58})),tt.set(this,{fds:r,args:e,env:n}),s&&et.set(this,s);const c=this;function u(t,e,n,r,s){c[t]=i?o?o.wrapImportFunction(st(c,t,n)):Z(st(c,t,n),r,s):st(c,t,e)}function f(t,e,n,r){const i=tt.get(this).fds.get(t,A.FD_FILESTAT_SET_TIMES,BigInt(0));return 2==(2&r)&&(e=BigInt(1e6*Date.now())),8==(8&r)&&(n=BigInt(1e6*Date.now())),{fileDescriptor:i,atim:e,mtim:n}}function h(t,e,n,r){const i=(e&(A.FD_READ|A.FD_READDIR))!==BigInt(0),s=(e&(A.FD_DATASYNC|A.FD_WRITE|A.FD_ALLOCATE|A.FD_FILESTAT_SET_SIZE))!==BigInt(0);let o=s?i?2:1:0,a=A.PATH_OPEN,c=e|n;return 0!=(1&t)&&(o|=64,a|=A.PATH_CREATE_FILE),0!=(2&t)&&(o|=65536),0!=(4&t)&&(o|=128),0!=(8&t)&&(o|=512,a|=A.PATH_FILESTAT_SET_SIZE),0!=(1&r)&&(o|=1024),0!=(2&r)&&(c|=A.FD_DATASYNC),0!=(4&r)&&(o|=2048),0!=(8&r)&&(o|=1052672,c|=A.FD_SYNC),0!=(16&r)&&(o|=1052672,c|=A.FD_SYNC),s&&0==(1536&o)&&(c|=A.FD_SEEK),{flags:o,needed_base:a,needed_inheriting:c}}u("fd_allocate",(function(t,e,n){const r=tt.get(this),i=rt(this),s=r.fds.get(t,A.FD_ALLOCATE,BigInt(0));return i.fstatSync(s.fd,{bigint:!0}).size<e+n&&i.ftruncateSync(s.fd,Number(e+n)),0}),(async function(t,e,n){const r=tt.get(this).fds.get(t,A.FD_ALLOCATE,BigInt(0)).fd;return(await r.stat({bigint:!0})).size<e+n&&await r.truncate(Number(e+n)),0}),["i32","i64","f64"],["i32"]),u("fd_close",(function(t){const e=tt.get(this),n=e.fds.get(t,BigInt(0),BigInt(0));return rt(this).closeSync(n.fd),e.fds.remove(t),0}),(async function(t){const e=tt.get(this),n=e.fds.get(t,BigInt(0),BigInt(0));return await n.fd.close(),e.fds.remove(t),0}),["i32"],["i32"]),u("fd_datasync",(function(t){const e=tt.get(this).fds.get(t,A.FD_DATASYNC,BigInt(0));return rt(this).fdatasyncSync(e.fd),0}),(async function(t){const e=tt.get(this).fds.get(t,A.FD_DATASYNC,BigInt(0));return await e.fd.datasync(),0}),["i32"],["i32"]),u("fd_filestat_get",(function(t,e){if(0===(e=Number(e)))return 28;const n=tt.get(this).fds.get(t,A.FD_FILESTAT_GET,BigInt(0)),r=rt(this).fstatSync(n.fd,{bigint:!0}),{view:i}=nt(this);return K(i,e,r),0}),(async function(t,e){if(0===(e=Number(e)))return 28;const n=tt.get(this).fds.get(t,A.FD_FILESTAT_GET,BigInt(0)).fd,r=await n.stat({bigint:!0}),{view:i}=nt(this);return K(i,e,r),0}),["i32","i32"],["i32"]),u("fd_filestat_set_size",(function(t,e){const n=tt.get(this).fds.get(t,A.FD_FILESTAT_SET_SIZE,BigInt(0));return rt(this).ftruncateSync(n.fd,Number(e)),0}),(async function(t,e){const n=tt.get(this).fds.get(t,A.FD_FILESTAT_SET_SIZE,BigInt(0)).fd;return await n.truncate(Number(e)),0}),["i32","i64"],["i32"]),u("fd_filestat_set_times",(function(t,e,n,r){if(gt(r))return 28;const{fileDescriptor:i,atim:s,mtim:o}=f.call(this,t,e,n,r);return rt(this).futimesSync(i.fd,Number(s),Number(o)),0}),(async function(t,e,n,r){if(gt(r))return 28;const{fileDescriptor:i,atim:s,mtim:o}=f.call(this,t,e,n,r),a=i.fd;return await a.utimes(Number(s),Number(o)),0}),["i32","i64","i64","i32"],["i32"]),u("fd_pread",(function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ft)return 28;const{HEAPU8:s,view:o}=nt(this),a=tt.get(this).fds.get(t,A.FD_READ|A.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;let c=0;const u=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return c+=a,s.subarray(i,i+a)}));let f=0;const h=(()=>{try{return new Uint8Array(new SharedArrayBuffer(c))}catch(t){return new Uint8Array(c)}})();h._isBuffer=!0;const g=rt(this).readSync(a.fd,h,0,h.length,Number(r));return f=h?Q(u,h.subarray(0,g)):0,o.setUint32(i,f,!0),0}),(async function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ft)return 28;const{HEAPU8:s,view:o}=nt(this),a=tt.get(this).fds.get(t,A.FD_READ|A.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;let c=0;const u=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return c+=a,s.subarray(i,i+a)}));let f=0;const h=new Uint8Array(c);h._isBuffer=!0;const{bytesRead:g}=await a.fd.read(h,0,h.length,Number(r));return f=h?Q(u,h.subarray(0,g)):0,o.setUint32(i,f,!0),0}),["i32","i32","i32","i64","i32"],["i32"]),u("fd_pwrite",(function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ft)return 28;const{HEAPU8:s,view:o}=nt(this),a=tt.get(this).fds.get(t,A.FD_WRITE|A.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;const c=k(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return s.subarray(i,i+a)}))),u=rt(this).writeSync(a.fd,c,0,c.length,Number(r));return o.setUint32(i,u,!0),0}),(async function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ft)return 28;const{HEAPU8:s,view:o}=nt(this),a=tt.get(this).fds.get(t,A.FD_WRITE|A.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;const c=k(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return s.subarray(i,i+a)}))),{bytesWritten:u}=await a.fd.write(c,0,c.length,Number(r));return o.setUint32(i,u,!0),0}),["i32","i32","i32","i64","i32"],["i32"]),u("fd_read",(function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=nt(this),o=tt.get(this).fds.get(t,A.FD_READ,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;let a=0;const c=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),c=s.getUint32(r+4,!0);return a+=c,i.subarray(o,o+c)}));let u,f=0;if(0===t){if("undefined"==typeof window||"function"!=typeof window.prompt)return 58;u=ht(),f=u?Q(c,u):0}else{u=(()=>{try{return new Uint8Array(new SharedArrayBuffer(a))}catch(t){return new Uint8Array(a)}})(),u._isBuffer=!0;const t=rt(this).readSync(o.fd,u,0,u.length,Number(o.pos));f=u?Q(c,u.subarray(0,t)):0,o.pos+=BigInt(f)}return s.setUint32(r,f,!0),0}),(async function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=nt(this),o=tt.get(this).fds.get(t,A.FD_READ,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;let a=0;const c=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),c=s.getUint32(r+4,!0);return a+=c,i.subarray(o,o+c)}));let u,f=0;if(0===t){if("undefined"==typeof window||"function"!=typeof window.prompt)return 58;u=ht(),f=u?Q(c,u):0}else{u=new Uint8Array(a),u._isBuffer=!0;const{bytesRead:t}=await o.fd.read(u,0,u.length,Number(o.pos));f=u?Q(c,u.subarray(0,t)):0,o.pos+=BigInt(f)}return s.setUint32(r,f,!0),0}),["i32","i32","i32","i32"],["i32"]),u("fd_readdir",(function(t,e,n,r,i){if(e=Number(e),n=Number(n),i=Number(i),0===e||0===i)return 0;const s=tt.get(this).fds.get(t,A.FD_READDIR,BigInt(0)),o=rt(this),a=o.readdirSync(s.realPath,{withFileTypes:!0}),{HEAPU8:c,view:u}=nt(this);let f=0;for(let t=Number(r);t<a.length;t++){const r=ct.encode(a[t].name),i=o.statSync(m(s.realPath,a[t].name),{bigint:!0}),u=new Uint8Array(24+r.byteLength),h=new DataView(u.buffer);let g;h.setBigUint64(0,BigInt(t+1),!0),h.setBigUint64(8,BigInt(i.ino?i.ino:0),!0),h.setUint32(16,r.byteLength,!0),g=a[t].isFile()?4:a[t].isDirectory()?3:a[t].isSymbolicLink()?7:a[t].isCharacterDevice()?2:a[t].isBlockDevice()?1:a[t].isSocket()?6:0,h.setUint8(20,g),u.set(r,24);const d=u.slice(0,Math.min(u.length,n-f));c.set(d,e+f),f+=d.byteLength}return u.setUint32(i,f,!0),0}),(async function(t,e,n,r,i){if(e=Number(e),n=Number(n),i=Number(i),0===e||0===i)return 0;const s=tt.get(this).fds.get(t,A.FD_READDIR,BigInt(0)),o=rt(this),a=await o.promises.readdir(s.realPath,{withFileTypes:!0}),{HEAPU8:c,view:u}=nt(this);let f=0;for(let t=Number(r);t<a.length;t++){const r=ct.encode(a[t].name),i=await o.promises.stat(m(s.realPath,a[t].name),{bigint:!0}),u=new Uint8Array(24+r.byteLength),h=new DataView(u.buffer);let g;h.setBigUint64(0,BigInt(t+1),!0),h.setBigUint64(8,BigInt(i.ino?i.ino:0),!0),h.setUint32(16,r.byteLength,!0),g=a[t].isFile()?4:a[t].isDirectory()?3:a[t].isSymbolicLink()?7:a[t].isCharacterDevice()?2:a[t].isBlockDevice()?1:a[t].isSocket()?6:0,h.setUint8(20,g),u.set(r,24);const d=u.slice(0,Math.min(u.length,n-f));c.set(d,e+f),f+=d.byteLength}return u.setUint32(i,f,!0),0}),["i32","i32","i32","i64","i32"],["i32"]),u("fd_renumber",(function(t,e){return tt.get(this).fds.renumber(e,t),0}),(async function(t,e){const n=tt.get(this);return await n.fds.renumber(e,t),0}),["i32","i32"],["i32"]),u("fd_sync",(function(t){const e=tt.get(this).fds.get(t,A.FD_SYNC,BigInt(0));return rt(this).fsyncSync(e.fd),0}),(async function(t){const e=tt.get(this).fds.get(t,A.FD_SYNC,BigInt(0));return await e.fd.sync(),0}),["i32"],["i32"]),u("fd_write",(function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=nt(this),o=tt.get(this).fds.get(t,A.FD_WRITE,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;const a=k(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),a=s.getUint32(r+4,!0);return i.subarray(o,o+a)})));let c;if(1===t||2===t)c=o.write(a);else{c=rt(this).writeSync(o.fd,a,0,a.length,Number(o.pos)),o.pos+=BigInt(c)}return s.setUint32(r,c,!0),0}),(async function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=nt(this),o=tt.get(this).fds.get(t,A.FD_WRITE,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;const a=k(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),a=s.getUint32(r+4,!0);return i.subarray(o,o+a)})));let c;return 1===t||2===t?c=o.write(a):(c=await(await o.fd.write(a,0,a.length,Number(o.pos))).bytesWritten,o.pos+=BigInt(c)),s.setUint32(r,c,!0),0}),["i32","i32","i32","i32"],["i32"]),u("path_create_directory",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=nt(this),i=tt.get(this).fds.get(t,A.PATH_CREATE_DIRECTORY,BigInt(0));let s=ut.decode(a(r,e,e+n));s=m(i.realPath,s);return rt(this).mkdirSync(s),0}),(async function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=nt(this),i=tt.get(this).fds.get(t,A.PATH_CREATE_DIRECTORY,BigInt(0));let s=ut.decode(a(r,e,e+n));s=m(i.realPath,s);const o=rt(this);return await o.promises.mkdir(s),0}),["i32","i32","i32"],["i32"]),u("path_filestat_get",(function(t,e,n,r,i){if(n=Number(n),r=Number(r),i=Number(i),0===n||0===i)return 28;const{HEAPU8:s,view:o}=nt(this),c=tt.get(this).fds.get(t,A.PATH_FILESTAT_GET,BigInt(0));let u=ut.decode(a(s,n,n+r));const f=rt(this);let h;return u=m(c.realPath,u),h=1==(1&e)?f.statSync(u,{bigint:!0}):f.lstatSync(u,{bigint:!0}),K(o,i,h),0}),(async function(t,e,n,r,i){if(n=Number(n),r=Number(r),i=Number(i),0===n||0===i)return 28;const{HEAPU8:s,view:o}=nt(this),c=tt.get(this).fds.get(t,A.PATH_FILESTAT_GET,BigInt(0));let u=ut.decode(a(s,n,n+r));const f=rt(this);let h;return u=m(c.realPath,u),h=1==(1&e)?await f.promises.stat(u,{bigint:!0}):await f.promises.lstat(u,{bigint:!0}),K(o,i,h),0}),["i32","i32","i32","i32","i32"],["i32"]),u("path_filestat_set_times",(function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),0===n)return 28;const{HEAPU8:c}=nt(this),u=tt.get(this).fds.get(t,A.PATH_FILESTAT_SET_TIMES,BigInt(0));if(gt(o))return 28;const f=rt(this),h=ot(f,u,ut.decode(a(c,n,n+r)),e);return 2==(2&o)&&(i=BigInt(1e6*Date.now())),8==(8&o)&&(s=BigInt(1e6*Date.now())),f.utimesSync(h,Number(i),Number(s)),0}),(async function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),0===n)return 28;const{HEAPU8:c}=nt(this),u=tt.get(this).fds.get(t,A.PATH_FILESTAT_SET_TIMES,BigInt(0));if(gt(o))return 28;const f=rt(this),h=await at(f,u,ut.decode(a(c,n,n+r)),e);return 2==(2&o)&&(i=BigInt(1e6*Date.now())),8==(8&o)&&(s=BigInt(1e6*Date.now())),await f.promises.utimes(h,Number(i),Number(s)),0}),["i32","i32","i32","i32","i64","i64","i32"],["i32"]),u("path_link",(function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),s=Number(s),o=Number(o),0===n||0===s)return 28;const c=tt.get(this);let u,f;t===i?u=f=c.fds.get(t,A.PATH_LINK_SOURCE|A.PATH_LINK_TARGET,BigInt(0)):(u=c.fds.get(t,A.PATH_LINK_SOURCE,BigInt(0)),f=c.fds.get(i,A.PATH_LINK_TARGET,BigInt(0)));const{HEAPU8:h}=nt(this),g=rt(this),d=ot(g,u,ut.decode(a(h,n,n+r)),e),l=m(f.realPath,ut.decode(a(h,s,s+o)));return g.linkSync(d,l),0}),(async function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),s=Number(s),o=Number(o),0===n||0===s)return 28;const c=tt.get(this);let u,f;t===i?u=f=c.fds.get(t,A.PATH_LINK_SOURCE|A.PATH_LINK_TARGET,BigInt(0)):(u=c.fds.get(t,A.PATH_LINK_SOURCE,BigInt(0)),f=c.fds.get(i,A.PATH_LINK_TARGET,BigInt(0)));const{HEAPU8:h}=nt(this),g=rt(this),d=await at(g,u,ut.decode(a(h,n,n+r)),e),l=m(f.realPath,ut.decode(a(h,s,s+o)));return await g.promises.link(d,l),0}),["i32","i32","i32","i32","i32","i32","i32"],["i32"]),u("path_open",(function(t,e,n,r,i,s,o,c,u){if(n=Number(n),u=Number(u),0===n||0===u)return 28;r=Number(r),s=BigInt(s),o=BigInt(o);const{flags:f,needed_base:g,needed_inheriting:d}=h(i,s,o,c),l=tt.get(this),_=l.fds.get(t,g,d),E=nt(this),p=E.HEAPU8,y=ut.decode(a(p,n,n+r)),I=rt(this),m=ot(I,_,y,e),A=I.openSync(m,f,438),b=l.fds.getFileTypeByFd(A);if(0!=(2&i)&&3!==b)return 54;const{base:T,inheriting:w}=O(l.fds.stdio,A,f,b),B=l.fds.insert(A,m,m,b,s&T,o&w,0),N=I.fstatSync(A,{bigint:!0});N.isFile()&&(B.size=N.size,0!=(1024&f)&&(B.pos=N.size));return E.view.setInt32(u,B.id,!0),0}),(async function(t,e,n,r,i,s,o,c,u){if(n=Number(n),u=Number(u),0===n||0===u)return 28;r=Number(r),s=BigInt(s),o=BigInt(o);const{flags:f,needed_base:g,needed_inheriting:d}=h(i,s,o,c),l=tt.get(this),_=l.fds.get(t,g,d),E=nt(this),p=E.HEAPU8,y=ut.decode(a(p,n,n+r)),I=rt(this),m=await at(I,_,y,e),A=await I.promises.open(m,f,438),b=await l.fds.getFileTypeByFd(A);if(0!=(2&i)&&3!==b)return 54;const{base:T,inheriting:w}=O(l.fds.stdio,A.fd,f,b),B=l.fds.insert(A,m,m,b,s&T,o&w,0),N=await A.stat({bigint:!0});N.isFile()&&(B.size=N.size,0!=(1024&f)&&(B.pos=N.size));return E.view.setInt32(u,B.id,!0),0}),["i32","i32","i32","i32","i32","i64","i64","i32","i32"],["i32"]),u("path_readlink",(function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),r=Number(r),i=Number(i),s=Number(s),0===e||0===r||0===s)return 28;const{HEAPU8:o,view:c}=nt(this),u=tt.get(this).fds.get(t,A.PATH_READLINK,BigInt(0));let f=ut.decode(a(o,e,e+n));f=m(u.realPath,f);const h=rt(this).readlinkSync(f),g=ct.encode(h),d=Math.min(g.length,i);return d>=i?42:(o.set(g.subarray(0,d),r),o[r+d]=0,c.setUint32(s,d,!0),0)}),(async function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),r=Number(r),i=Number(i),s=Number(s),0===e||0===r||0===s)return 28;const{HEAPU8:o,view:c}=nt(this),u=tt.get(this).fds.get(t,A.PATH_READLINK,BigInt(0));let f=ut.decode(a(o,e,e+n));f=m(u.realPath,f);const h=rt(this),g=await h.promises.readlink(f),d=ct.encode(g),l=Math.min(d.length,i);return l>=i?42:(o.set(d.subarray(0,l),r),o[r+l]=0,c.setUint32(s,l,!0),0)}),["i32","i32","i32","i32","i32","i32"],["i32"]),u("path_remove_directory",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=nt(this),i=tt.get(this).fds.get(t,A.PATH_REMOVE_DIRECTORY,BigInt(0));let s=ut.decode(a(r,e,e+n));s=m(i.realPath,s);return rt(this).rmdirSync(s),0}),(async function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=nt(this),i=tt.get(this).fds.get(t,A.PATH_REMOVE_DIRECTORY,BigInt(0));let s=ut.decode(a(r,e,e+n));s=m(i.realPath,s);const o=rt(this);return await o.promises.rmdir(s),0}),["i32","i32","i32"],["i32"]),u("path_rename",(function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),i=Number(i),s=Number(s),0===e||0===i)return 28;const o=tt.get(this);let c,u;t===r?c=u=o.fds.get(t,A.PATH_RENAME_SOURCE|A.PATH_RENAME_TARGET,BigInt(0)):(c=o.fds.get(t,A.PATH_RENAME_SOURCE,BigInt(0)),u=o.fds.get(r,A.PATH_RENAME_TARGET,BigInt(0)));const{HEAPU8:f}=nt(this),h=m(c.realPath,ut.decode(a(f,e,e+n))),g=m(u.realPath,ut.decode(a(f,i,i+s)));return rt(this).renameSync(h,g),0}),(async function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),i=Number(i),s=Number(s),0===e||0===i)return 28;const o=tt.get(this);let c,u;t===r?c=u=o.fds.get(t,A.PATH_RENAME_SOURCE|A.PATH_RENAME_TARGET,BigInt(0)):(c=o.fds.get(t,A.PATH_RENAME_SOURCE,BigInt(0)),u=o.fds.get(r,A.PATH_RENAME_TARGET,BigInt(0)));const{HEAPU8:f}=nt(this),h=m(c.realPath,ut.decode(a(f,e,e+n))),g=m(u.realPath,ut.decode(a(f,i,i+s))),d=rt(this);return await d.promises.rename(h,g),0}),["i32","i32","i32","i32","i32","i32"],["i32"]),u("path_symlink",(function(t,e,n,r,i){if(t=Number(t),e=Number(e),r=Number(r),i=Number(i),0===t||0===r)return 28;const{HEAPU8:s}=nt(this),o=tt.get(this).fds.get(n,A.PATH_SYMLINK,BigInt(0)),c=ut.decode(a(s,t,t+e));let u=ut.decode(a(s,r,r+i));u=m(o.realPath,u);return rt(this).symlinkSync(c,u),0}),(async function(t,e,n,r,i){if(t=Number(t),e=Number(e),r=Number(r),i=Number(i),0===t||0===r)return 28;const{HEAPU8:s}=nt(this),o=tt.get(this).fds.get(n,A.PATH_SYMLINK,BigInt(0)),c=ut.decode(a(s,t,t+e));let u=ut.decode(a(s,r,r+i));u=m(o.realPath,u);const f=rt(this);return await f.promises.symlink(c,u),0}),["i32","i32","i32","i32","i32"],["i32"]),u("path_unlink_file",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=nt(this),i=tt.get(this).fds.get(t,A.PATH_UNLINK_FILE,BigInt(0));let s=ut.decode(a(r,e,e+n));s=m(i.realPath,s);return rt(this).unlinkSync(s),0}),(async function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=nt(this),i=tt.get(this).fds.get(t,A.PATH_UNLINK_FILE,BigInt(0));let s=ut.decode(a(r,e,e+n));s=m(i.realPath,s);const o=rt(this);return await o.promises.unlink(s),0}),["i32","i32","i32"],["i32"]),this._setMemory=function(e){if(!(e instanceof t.Memory))throw new TypeError('"instance.exports.memory" property must be a WebAssembly.Memory');J.set(c,$(e))}}static createSync(t,e,n,r,i,s,o){const a=new z({size:3,in:r[0],out:r[1],err:r[2],fs:i,print:s,printErr:o}),c=new dt(t,e,a,!1,i);if(n.length>0)for(let t=0;t<n.length;++t){const e=i.realpathSync(n[t].realPath,"utf8"),r=i.openSync(e,"r",438);a.insertPreopen(r,n[t].mappedPath,e)}return c}static async createAsync(t,e,n,r,i,s,o,a){const c=new G({size:3,in:r[0],out:r[1],err:r[2],print:s,printErr:o}),u=new dt(t,e,c,!0,i,a);if(n.length>0)for(let t=0;t<n.length;++t){const e=n[t],r=await i.promises.realpath(e.realPath),s=await i.promises.open(r,"r",438);await c.insertPreopen(s,e.mappedPath,r)}return u}}const lt=Object.freeze(Object.create(null)),_t=Symbol("kExitCode"),Et=Symbol("kSetMemory"),pt=Symbol("kStarted"),yt=Symbol("kInstance"),It=Symbol("kBindingName");function mt(t,n){e(n,"instance"),e(n.exports,"instance.exports"),t[yt]=n,t[Et](n.exports.memory)}function At(t){var i;let s;if(e(t,"options"),void 0!==t.version)switch(n(t.version,"options.version"),t.version){case"unstable":s=dt,this[It]="wasi_unstable";break;case"preview1":s=dt,this[It]="wasi_snapshot_preview1";break;default:throw new TypeError(`unsupported WASI version "${t.version}"`)}else s=dt,this[It]="wasi_snapshot_preview1";void 0!==t.args&&function(t,e){if(!Array.isArray(t))throw new TypeError(`${e} must be an array. Received ${null===t?"null":typeof t}`)}(t.args,"options.args");const o=(null!==(i=t.args)&&void 0!==i?i:[]).map(String),a=[];void 0!==t.env&&(e(t.env,"options.env"),Object.entries(t.env).forEach((({0:t,1:e})=>{void 0!==e&&a.push(`${t}=${e}`)})));const c=[];if(void 0!==t.preopens&&(e(t.preopens,"options.preopens"),Object.entries(t.preopens).forEach((({0:t,1:e})=>c.push({mappedPath:String(t),realPath:String(e)})))),c.length>0){if(void 0===t.fs)throw new Error("filesystem is disabled, can not preopen directory");try{e(t.fs,"options.fs")}catch(t){throw new TypeError("Node.js fs like implementation is not provided")}}void 0!==t.print&&r(t.print,"options.print"),void 0!==t.printErr&&r(t.printErr,"options.printErr"),void 0!==t.returnOnExit&&function(t,e){if("boolean"!=typeof t)throw new TypeError(`${e} must be a boolean. Received ${null===t?"null":typeof t}`)}(t.returnOnExit,"options.returnOnExit");return{args:o,env:a,preopens:c,stdio:[0,1,2],_WASI:s}}function bt(t,e){this[Et]=t,this.wasiImport=e,this[pt]=!1,this[_t]=0,this[yt]=void 0}class Tt{constructor(t=lt){const{args:e,env:n,preopens:r,stdio:i,_WASI:s}=At.call(this,t),o=s.createSync(e,n,r,i,t.fs,t.print,t.printErr),a=o._setMemory;delete o._setMemory,bt.call(this,a,o),t.returnOnExit&&(o.proc_exit=wt.bind(this))}start(t){if(this[pt])throw new Error("WASI instance has already started");this[pt]=!0,mt(this,t);const{_start:e,_initialize:n}=this[yt].exports;let s;r(e,"instance.exports._start"),i(n,"instance.exports._initialize");try{s=e()}catch(t){if(t!==_t)throw t}return s instanceof Promise?s.then((()=>this[_t]),(t=>{if(t!==_t)throw t;return this[_t]})):this[_t]}initialize(t){if(this[pt])throw new Error("WASI instance has already started");this[pt]=!0,mt(this,t);const{_start:e,_initialize:n}=this[yt].exports;if(i(e,"instance.exports._start"),void 0!==n)return r(n,"instance.exports._initialize"),n()}getImportObject(){return{[this[It]]:this.wasiImport}}}function wt(t){throw this[_t]=t,_t}async function Bt(t=lt){const n=Object.create(Tt.prototype),{args:i,env:s,preopens:o,stdio:a,_WASI:c}=At.call(n,t);void 0!==t.asyncify&&(e(t.asyncify,"options.asyncify"),r(t.asyncify.wrapImportFunction,"options.asyncify.wrapImportFunction"));const u=await c.createAsync(i,s,o,a,t.fs,t.print,t.printErr,t.asyncify),f=u._setMemory;return delete u._setMemory,bt.call(n,f,u),t.returnOnExit&&(u.proc_exit=wt.bind(n)),n}export{f as Asyncify,Y as Memory,Tt as WASI,j as WebAssemblyMemory,l as asyncifyLoad,E as asyncifyLoadSync,Bt as createAsyncWASI,$ as extendMemory,d as load,_ as loadSync,q as wrapAsyncExport,Z as wrapAsyncImport,X as wrapExports};
