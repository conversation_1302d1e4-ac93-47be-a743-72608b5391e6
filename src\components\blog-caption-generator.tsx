'use client';

import React, { useState } from 'react';
import { Copy, Wand2, FileText, Hash, Users, Megaphone } from 'lucide-react';

const BlogCaptionGenerator = () => {
  const [inputs, setInputs] = useState({
    topic: '',
    tone: 'professional',
    audience: '',
    contentType: 'blog',
    length: 'medium'
  });
  
  const [generatedContent, setGeneratedContent] = useState('');
  const [seoKeywords, setSeoKeywords] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  const toneOptions = [
    { value: 'professional', label: 'Professional' },
    { value: 'casual', label: 'Casual' },
    { value: 'friendly', label: 'Friendly' },
    { value: 'authoritative', label: 'Authoritative' },
    { value: 'conversational', label: 'Conversational' },
    { value: 'persuasive', label: 'Persuasive' }
  ];

  const contentTypes = [
    { value: 'blog', label: 'Blog Post', icon: FileText },
    { value: 'instagram', label: 'Instagram Caption', icon: Megaphone },
    { value: 'linkedin', label: 'LinkedIn Post', icon: Users },
    { value: 'twitter', label: 'Twitter Thread', icon: Hash }
  ];

  const lengthOptions = [
    { value: 'short', label: 'Short' },
    { value: 'medium', label: 'Medium' },
    { value: 'long', label: 'Long' }
  ];

  const handleInputChange = (field, value) => {
    setInputs(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateContent = async () => {
    if (!inputs.topic || !inputs.audience) {
      alert('Please fill in both topic and target audience fields');
      return;
    }

    setIsGenerating(true);
    
    try {
      // Call the API route to generate content using OpenRouter
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: inputs.topic,
          tone: inputs.tone,
          audience: inputs.audience,
          contentType: inputs.contentType,
          length: inputs.length,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate content');
      }

      const parsedResponse = await response.json();
      
      setGeneratedContent(parsedResponse.content);
      setSeoKeywords(parsedResponse.seoKeywords || []);
    } catch (error) {
      console.error('Error generating content:', error);
      alert('Error generating content. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text');
    }
  };

  const getCharacterCount = () => {
    return generatedContent.length;
  };

  const getCharacterLimit = () => {
    switch (inputs.contentType) {
      case 'twitter': return 280;
      case 'instagram': return 2200;
      case 'linkedin': return 3000;
      default: return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">Blog & Caption Generator</h1>
          <p className="text-gray-600">Create engaging content with SEO optimization in seconds</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
              <Wand2 className="mr-2 text-blue-600" />
              Content Settings
            </h2>

            <div className="space-y-6">
              {/* Topic Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Topic *
                </label>
                <input
                  type="text"
                  value={inputs.topic}
                  onChange={(e) => handleInputChange('topic', e.target.value)}
                  placeholder="e.g., Digital Marketing Trends 2025"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Target Audience */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience *
                </label>
                <input
                  type="text"
                  value={inputs.audience}
                  onChange={(e) => handleInputChange('audience', e.target.value)}
                  placeholder="e.g., Small business owners, Marketing professionals"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Content Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Type
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {contentTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <button
                        key={type.value}
                        onClick={() => handleInputChange('contentType', type.value)}
                        className={`p-3 rounded-lg border-2 flex items-center justify-center text-sm font-medium transition-all ${
                          inputs.contentType === type.value
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 text-gray-600 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="w-4 h-4 mr-1" />
                        {type.label}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Tone Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tone
                </label>
                <select
                  value={inputs.tone}
                  onChange={(e) => handleInputChange('tone', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {toneOptions.map((tone) => (
                    <option key={tone.value} value={tone.value}>
                      {tone.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Length Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Length
                </label>
                <div className="flex space-x-2">
                  {lengthOptions.map((length) => (
                    <button
                      key={length.value}
                      onClick={() => handleInputChange('length', length.value)}
                      className={`flex-1 p-3 rounded-lg border-2 text-sm font-medium transition-all ${
                        inputs.length === length.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 text-gray-600 hover:border-gray-300'
                      }`}
                    >
                      {length.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Generate Button */}
              <button
                onClick={generateContent}
                disabled={isGenerating || !inputs.topic || !inputs.audience}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Wand2 className="mr-2" />
                    Generate Content
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Output Panel */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-gray-800 flex items-center">
                <FileText className="mr-2 text-green-600" />
                Generated Content
              </h2>
              {generatedContent && (
                <button
                  onClick={copyToClipboard}
                  className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  {copied ? 'Copied!' : 'Copy'}
                </button>
              )}
            </div>

            {generatedContent ? (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-gray-800 font-sans leading-relaxed">
                    {generatedContent}
                  </pre>
                </div>

                {/* Character Count */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Characters: {getCharacterCount()}</span>
                  {getCharacterLimit() && (
                    <span className={getCharacterCount() > (getCharacterLimit() || 0) ? 'text-red-500' : ''}>
                      Limit: {getCharacterLimit()}
                    </span>
                  )}
                </div>

                {/* SEO Keywords */}
                {seoKeywords.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-700 mb-2 flex items-center">
                      <Hash className="w-4 h-4 mr-1" />
                      SEO Keywords
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {seoKeywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p>Your generated content will appear here</p>
                <p className="text-sm mt-2">Fill in the form and click "Generate Content" to get started</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogCaptionGenerator;