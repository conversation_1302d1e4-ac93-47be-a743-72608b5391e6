import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { topic, tone, audience, contentType, length } = await request.json();

    // Validate required fields
    if (!topic || !audience) {
      return NextResponse.json(
        { error: 'Topic and audience are required' },
        { status: 400 }
      );
    }

    // Check for OpenRouter API key
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }

    // Create the prompt based on content type and parameters
    const prompt = `Create ${contentType} content with the following specifications:

Topic: ${topic}
Tone: ${tone}
Target Audience: ${audience}
Content Length: ${length}

Requirements:
1. Generate engaging, ${tone} content appropriate for ${contentType}
2. Include relevant SEO keywords naturally integrated into the content
3. Structure the content appropriately for the platform
4. Make it compelling for the target audience: ${audience}
5. For blog posts, use proper markdown formatting with headers
6. For social media, include appropriate hashtags and emojis

Please respond with a JSON object in this exact format:
{
  "content": "The generated content here",
  "seoKeywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
}

Your entire response MUST be a single, valid JSON object. Do not include any text outside of the JSON structure.`;

    // Call OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'Blog Caption Generator',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet', // You can change this to other models
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json(
        { error: 'Failed to generate content' },
        { status: 500 }
      );
    }

    const data = await response.json();
    const generatedText = data.choices[0]?.message?.content;

    if (!generatedText) {
      return NextResponse.json(
        { error: 'No content generated' },
        { status: 500 }
      );
    }

    // Parse the JSON response from the AI
    try {
      const parsedContent = JSON.parse(generatedText);
      return NextResponse.json(parsedContent);
    } catch (parseError) {
      // If JSON parsing fails, create a fallback response
      console.error('Failed to parse AI response as JSON:', parseError);
      return NextResponse.json({
        content: generatedText,
        seoKeywords: [
          topic.toLowerCase(),
          audience.toLowerCase().split(' ')[0],
          contentType,
          tone,
          'content marketing'
        ]
      });
    }

  } catch (error) {
    console.error('API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
