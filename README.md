# Blog & Caption Generator

A modern web application for generating engaging blog posts and social media captions with SEO optimization.

## Features

- **Multi-Platform Content**: Generate content for blogs, Instagram, LinkedIn, and Twitter
- **Customizable Tone**: Choose from professional, casual, friendly, authoritative, conversational, or persuasive tones
- **Target Audience Focused**: Tailor content for specific audiences
- **SEO Optimization**: Automatically generates relevant SEO keywords
- **Character Counting**: Platform-specific character limits and counting
- **Copy to Clipboard**: Easy content copying functionality
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Set up OpenRouter API (for AI content generation):
   - Get your API key from [OpenRouter.ai](https://openrouter.ai/)
   - Copy `.env.local` and add your API key:
   ```env
   OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here
   ```
   - See `OPENROUTER_SETUP.md` for detailed instructions

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. Enter your content topic
2. Specify your target audience
3. Select content type (Blog, Instagram, LinkedIn, Twitter)
4. Choose tone and length preferences
5. Click "Generate Content" to create your content
6. Copy the generated content using the copy button

## Tech Stack

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Lucide React** - Icons
- **React Hooks** - State management
- **OpenRouter API** - AI content generation with multiple model options

## Project Structure

```
src/
├── app/
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Home page
│   └── globals.css     # Global styles
└── components/
    └── blog-caption-generator.tsx  # Main component
```
