# 🚀 AI Blog & Caption Generator

[![Next.js](https://img.shields.io/badge/Next.js-15-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![OpenRouter](https://img.shields.io/badge/OpenRouter-AI-purple?style=flat-square)](https://openrouter.ai/)

A modern, AI-powered web application that generates engaging blog posts and social media captions with SEO optimization. Built with Next.js 15 and powered by multiple AI models through OpenRouter.

![Blog Caption Generator Demo](https://via.placeholder.com/800x400/4F46E5/FFFFFF?text=Blog+%26+Caption+Generator)

## ✨ Features

### 🎯 **Multi-Platform Content Generation**
- **Blog Posts** - Long-form content with proper markdown formatting
- **Instagram Captions** - Engaging posts with hashtags and emojis
- **LinkedIn Posts** - Professional content for business networking
- **Twitter Threads** - Concise, impactful social media content

### 🎨 **Customization Options**
- **6 Tone Styles**: Professional, Casual, Friendly, Authoritative, Conversational, Persuasive
- **3 Length Options**: Short, Medium, Long content
- **Target Audience**: Tailored content for specific demographics

### 🔧 **Smart Features**
- **SEO Optimization** - Automatically generates relevant keywords
- **Character Counting** - Platform-specific limits with visual indicators
- **One-Click Copy** - Easy content copying to clipboard
- **Responsive Design** - Perfect on desktop, tablet, and mobile
- **Real-time Generation** - Powered by state-of-the-art AI models

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- OpenRouter API key ([Get one here](https://openrouter.ai/keys))

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/blog-caption-generator.git
   cd blog-caption-generator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your OpenRouter API key:
   ```env
   OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Usage Guide

### Basic Workflow
1. **Enter Topic** - Describe what you want to write about
2. **Define Audience** - Specify your target readers
3. **Choose Platform** - Select blog, Instagram, LinkedIn, or Twitter
4. **Set Tone & Length** - Customize the style and size
5. **Generate** - Click to create AI-powered content
6. **Copy & Use** - One-click copy to your clipboard

### Example Input
```
Topic: "Digital Marketing Trends 2025"
Audience: "Small business owners"
Platform: "LinkedIn Post"
Tone: "Professional"
Length: "Medium"
```

### Example Output
The AI will generate professional LinkedIn content with relevant hashtags, SEO keywords, and proper formatting for maximum engagement.

## 🛠️ Tech Stack

| Technology | Purpose | Version |
|------------|---------|---------|
| **Next.js** | React Framework | 15.x |
| **TypeScript** | Type Safety | 5.x |
| **Tailwind CSS** | Styling | 3.x |
| **Lucide React** | Icons | Latest |
| **OpenRouter API** | AI Models | Latest |

### AI Models Available
- **Claude 3.5 Sonnet** (Default) - Excellent for content generation
- **GPT-4** - OpenAI's flagship model
- **GPT-3.5 Turbo** - Fast and cost-effective
- **Llama 3.1** - Meta's open-source model
- **Gemini Pro** - Google's advanced model

## 📁 Project Structure

```
blog-caption-generator/
├── 📁 src/
│   ├── 📁 app/
│   │   ├── 📁 api/
│   │   │   └── 📁 generate/
│   │   │       └── route.ts          # OpenRouter API integration
│   │   ├── layout.tsx                # Root layout
│   │   ├── page.tsx                  # Home page
│   │   └── globals.css               # Global styles
│   └── 📁 components/
│       └── blog-caption-generator.tsx # Main component
├── 📁 public/                        # Static assets
├── .env.example                      # Environment template
├── .env.local                        # Your API keys (gitignored)
├── OPENROUTER_SETUP.md              # Detailed setup guide
└── README.md                         # This file
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `OPENROUTER_API_KEY` | Your OpenRouter API key | ✅ Yes | - |
| `NEXT_PUBLIC_SITE_URL` | Site URL for referrer header | ❌ No | `http://localhost:3000` |

### Model Configuration

To change the AI model, edit `src/app/api/generate/route.ts`:

```typescript
// Line 42 - Change the model
model: 'anthropic/claude-3.5-sonnet', // Your preferred model
```

Popular alternatives:
- `openai/gpt-4o` - Latest GPT-4 model
- `openai/gpt-3.5-turbo` - Faster, cheaper option
- `meta-llama/llama-3.1-8b-instruct:free` - Free option
- `google/gemini-pro` - Google's model

## 📊 Performance & Pricing

### Response Times
- **Average Generation**: 3-8 seconds
- **Model Dependent**: Claude (faster) vs GPT-4 (slower but higher quality)

### Cost Estimation (OpenRouter)
- **Claude 3.5 Sonnet**: ~$0.003 per generation
- **GPT-4**: ~$0.01 per generation
- **GPT-3.5 Turbo**: ~$0.001 per generation
- **Free Models**: $0 (with rate limits)

## 🚀 Deployment

### Vercel (Recommended)
1. Push to GitHub
2. Connect to [Vercel](https://vercel.com)
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
- **Netlify**: Supports Next.js with serverless functions
- **Railway**: Simple deployment with environment variables
- **Docker**: Use the included Dockerfile (if added)

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Use Tailwind CSS for styling
- Add proper error handling
- Include tests for new features
- Update documentation

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenRouter** - For providing access to multiple AI models
- **Vercel** - For the amazing Next.js framework
- **Tailwind CSS** - For the utility-first CSS framework
- **Lucide** - For the beautiful icons

## 📞 Support

- **Documentation**: Check `OPENROUTER_SETUP.md` for detailed setup
- **Issues**: [GitHub Issues](https://github.com/yourusername/blog-caption-generator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/blog-caption-generator/discussions)

## 🔮 Roadmap

- [ ] **Batch Generation** - Generate multiple variations
- [ ] **Content Templates** - Pre-built templates for different industries
- [ ] **Analytics Dashboard** - Track generation history and performance
- [ ] **Team Collaboration** - Share and collaborate on content
- [ ] **API Access** - RESTful API for developers
- [ ] **Browser Extension** - Generate content directly in social media platforms

---

<div align="center">

**⭐ Star this repo if you find it helpful!**

Made with ❤️ by [Your Name](https://github.com/yourusername)

</div>
